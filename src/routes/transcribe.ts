import * as express from "express";
import { transcribe } from "../controller/transcribe";
import OpenAI from "openai";

const openAi = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
})

const transcribeRouter = express.Router();

// GET endpoint that returns hello world
transcribeRouter.get("/", (req: express.Request, res: express.Response) => {
    res.json({ message: "hello world" });
});

transcribeRouter.post("/", transcribe);

export default transcribeRouter;
