import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";

import {
    ChatCompletionParams,
    generateChatCompletion,
} from "../services/inference/generateChatCompletion";
import { handleErrors } from "../services/chatMaestro/sendToMaestro";

/**
 * Custom error class for timeout errors
 */
class TimeoutError extends Error {
    constructor(message: string = 'Operation timed out') {
        super(message);
        this.name = 'TimeoutError';
    }
}

export async function createInferenceChatCompletionController(
    req: Request,
    res: Response
) {
    const params: ChatCompletionParams = req.body;
    const { user } = req;

    // Track the execution start time for proper expiration calculation
    const executionStartTime = new Date();

    // Add the start time to params so generateChatCompletion can use it
    const paramsWithStartTime = {
        ...params,
        startedAt: executionStartTime
    };

    let completionPromise: Promise<any> | null = null;
    let timeoutId: NodeJS.Timeout | null = null;

    try {
        // Start the generation process
        completionPromise = generateChatCompletion(paramsWithStartTime, user);

        // Set up timeout that will distribute to maestro and stop generation
        const timeoutPromise = new Promise<never>((_, reject) => {
            timeoutId = setTimeout(async () => {
                console.log('Chat completion timed out after 2.5 minutes, distributing to chat maestro');

                try {
                    // Distribute the task to chat maestro when timeout occurs
                    const maestroResult = await handleErrors({
                        resText: "",
                        alert: "TIMEOUT: Chat completion timed out after 2.5 minutes",
                        prompt: { name: params.siteInfos.metaData.type ?? "default" },
                        siteInfos: params.siteInfos,
                        extractedMetadata: {},
                        startedAt: executionStartTime, // Use the original execution start time
                        summary: {
                            assistant: {},
                            user: {}
                        },
                        disableAutoSend: true
                    });

                    // Reject with the maestro result instead of an error
                    reject({ isMaestroResult: true, result: maestroResult });
                } catch (maestroError) {
                    console.error('Error distributing to chat maestro:', maestroError);
                    reject(new TimeoutError('Chat completion timed out and maestro distribution failed'));
                }
            }, 150000); // 2.5 minutes in milliseconds
        });

        // Race between the actual operation and the timeout
        const completionRes = await Promise.race([
            completionPromise,
            timeoutPromise
        ]);

        // Clear timeout if completion finished first
        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        return res.status(StatusCodes.OK).json(completionRes);
    } catch (error) {
        // Clear timeout in case of any error
        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        // Check if this is a maestro result from timeout
        if (error && typeof error === 'object' && 'isMaestroResult' in error) {
            console.log('Returning maestro result after timeout');
            return res.status(StatusCodes.OK).json((error as any).result);
        }

        if (error instanceof TimeoutError) {
            console.error('Chat completion timed out:', error.message);
            return res.status(StatusCodes.REQUEST_TIMEOUT).json({
                error: 'Request timed out',
                message: 'The chat completion operation took too long to complete'
            });
        }

        // Handle other errors
        console.error('Error in chat completion:', error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            error: 'Internal server error',
            message: 'An unexpected error occurred'
        });
    }
}
