import OpenAI from "openai";
import {
    createAnalyticsItem,
    generateAnalyticsItem,
} from "../supabase/chatCompleteAnalyticsService";
import { getPromptConfigDB, getPromptDB } from "../supabase/promptConfig";
import { cleanupOutput } from "../../util/cleanup";
import {
    getPromptTypeChatCompletion,
    PromptType,
} from "../promptType/promptTypeChatCompletion";
import { sendLogSnag } from "../logsnag";
import { handleInferenceChatCompletion } from "./inferenceChatComplete";
import { currentDayTime } from "../../util/daytimeCompute";
import { messagesToOpenAIMessages } from "./messagesToOpenAIMessages";
import { computeNearbyCity } from "./computeNearbyCity";
import { createSummary } from "../summarizer/summary";
import DailyInfo from "../../models/DailyInfo";
import {
    getCityCoordinates,
    getNearbyCityPostalCodePrefix,
    Location,
} from "../googlePlaces/nearbyCity";
import { getDailyInfo } from "../dailyInfo";
import {
    ComputedMetadata,
    ExtractedMetadata,
    ImageAnalysisData,
    SiteInfos,
} from "../../models/SiteInfos";
import { extractMetadata } from "../dataExtraction/extractMetadata";
import { mergePrompt } from "./mergePrompt";
import { ChatCompletionMessageParam } from "openai/resources";
import { structureNotesFromRawtext } from "../dataExtraction/structureNotesFromRawtext";
import { checkQwqOutput } from "../outputChecker/qwq/qwqOutputChecker";
import lengthChecker from "../outputChecker/lengthChecker/lengthChecker";
import ligamentolysis from "../outputChecker/lengthChecker/ligamentolysis";
import checkForBlockedPhrases from "../forbiddenWords";
import { findAsset } from "../vison/visionServer";
import { inferenceGemini } from "./inferenceGemini";
import { appendEmoji } from "../../util/emojiAppender";
import { extractUserCity } from "../dataExtraction/extractCityFromConvo";
import profilePicAnalysis from "./profilePicAnalysis";
import { fallbackGemini } from "./fallbackGemini";
import messageForAssets from "./messageForAssets";
import rewriteResponse from "../rewrite/rewriteResponse";
import {
    getRewrittenAttributes,
    ModeratorRewriteInfos,
} from "../rewrite/rewriteAttributes";
import createTemplateTW from "./torchwoodTemplates/createTemplateTW";
import { isGerman } from "../outputChecker/checkGerman";
const BACKEND_VERSION = "1.0.24";
import {
    handleErrors,
    hasTimeThresholdExceeded
} from "../chatMaestro/sendToMaestro";

export interface ChatCompletionParams {
    siteInfos: SiteInfos;
    // the next ones are computed after the request
    computedMetadata?: ComputedMetadata;
    runCount?: number;
    extractedMetadata?: ExtractedMetadata;
    startedAt?: Date;
}

interface ChatCompletionResult {
    resText: string | any;
    promptType: string;
    alert?: string;
    summary?: {
        assistant: Record<string, string>;
        user: Record<string, string>;
    };
    analyticsItemId?: number;
    extractedMetadata?: ExtractedMetadata;
    prompt?: string;
    chatId?: string;
    disableAutoSend: boolean;
    assetsToSend?: {
        analysis: string;
        keywords: string[];
        model: "gemini" | "grok";
        imageUrl: string;
        processedAt: Date;
        resText?: string;
    }[];
}

const normalizeCountry = (country?: string): "DE" | "CH" | "AT" | undefined => {
    if (!country) return undefined;
    const normalized = country.toLowerCase().trim();
    if (
        normalized === "germany" ||
        normalized === "deutschland" ||
        normalized === "de"
    )
        return "DE";
    if (
        normalized === "austria" ||
        normalized === "österreich" ||
        normalized === "at"
    )
        return "AT";
    if (
        normalized === "switzerland" ||
        normalized === "schweiz" ||
        normalized === "ch"
    )
        return "CH";
    return undefined;
};

export const generateChatCompletion = async (
    params: ChatCompletionParams,
    user: {
        id: string;
        email: string;
        aud: string;
    }
): Promise<ChatCompletionResult | null> => {
    let startedAt = params.startedAt ?? new Date();
    let maintanenceEmails: string[] = [];

    let selectPhotoToSend = false;

    if (
        maintanenceEmails.length > 0 &&
        maintanenceEmails.includes(user.email) == false
    ) {
        return {
            resText: "",
            promptType: "default",
            alert: "SERVER MAINTANENCE",
            disableAutoSend: true,
        };
    }

    let { siteInfos, runCount = 1 } = params;

    if (siteInfos == undefined || siteInfos.metaData == undefined) {
        return {
            resText: "",
            promptType: "default",
            alert: "UPDATE EXTENSION",
            disableAutoSend: true,
        };
    }

    let { origin, messages, html, extensionVersion } = siteInfos;

    if (origin == "test") origin = "gold";
    if (!html) html = "";

    if (
        origin == "torchwood" &&
        messages.length > 0 &&
        siteInfos.metaData.ins &&
        siteInfos.metaData.ins <= 10
    ) {
        console.log(
            "checking if last message is german...",
            messages[messages.length - 1].text
        );
        const _isGerman = await isGerman(
            messages[messages.length - 1].text,
            origin
        );

        console.log("isGerman", _isGerman);

        if (_isGerman == false) {
            return {
                resText: "",
                promptType: "default",
                alert: "NON GERMAN MESSAGE DETECTED",
                disableAutoSend: true,
            };
        }
    }

    if (parseFloat(extensionVersion) < parseFloat(BACKEND_VERSION)) {
        return {
            resText: "",
            promptType: "default",
            alert: `UPDATE EXTENSION\ncurrent version: ${extensionVersion}\nnew version: ${BACKEND_VERSION}`,
            disableAutoSend: true,
        };
    }

    if (
        siteInfos.metaData.moderatorInfo.country ||
        siteInfos.metaData.customerInfo.country ||
        siteInfos.metaData.customerNotes?.country ||
        siteInfos.metaData.moderatorNotes?.country
    ) {
        siteInfos.metaData.moderatorInfo.country = normalizeCountry(
            siteInfos.metaData.moderatorInfo.country
        );
        siteInfos.metaData.customerInfo.country = normalizeCountry(
            siteInfos.metaData.customerInfo.country
        );
    }

    if (siteInfos.metaData.customerNotes) {
        siteInfos.metaData.customerNotes.country = normalizeCountry(
            siteInfos.metaData.customerNotes.country
        );
    }

    if (siteInfos.metaData.moderatorNotes) {
        siteInfos.metaData.moderatorNotes.country = normalizeCountry(
            siteInfos.metaData.moderatorNotes.country
        );
    }

    if (
        !siteInfos.metaData.customerInfo.gender?.trim() ||
        !siteInfos.metaData.moderatorInfo.gender?.trim()
    ) {
        return await handleErrors({
            resText: "",
            siteInfos,
            alert: "GENDER READ WRONG",
            startedAt,
            prompt: {  name: "default" },
            summary: {
                assistant: {},
                user: {}
            },
            extractedMetadata: {},
            disableAutoSend: true
        });
    }

    if (runCount > 3) {
        await sendLogSnag("choked", origin);
        return await handleErrors({
            resText: "",
            siteInfos,
            alert: "Our safety systems detected problematic outputs. Please let a human check the conversation.",
            prompt: { name: siteInfos.metaData.type ?? "default", },
            summary: {
                assistant: {},
                user: {}
            },
            startedAt,
            extractedMetadata: {},
            disableAutoSend: true
        });
    }

    const { html: _, ...siteInfosWithoutHtml } = siteInfos;

    if (origin == "fpc") {
        if (
            siteInfos.metaData.alertBoxMessages?.includes(
                "Der Kunde hat private Fotos angefordert."
            )
        ) {
            selectPhotoToSend = true;

            return {
                resText: "",
                promptType: siteInfos.metaData.type ?? "default",
                alert: "CUSTOMER WANTS PHOTO",
                disableAutoSend: true,
            };
        }
    }

    if (messages.length == 0) {
        siteInfos.metaData.sessionStart = undefined;
    }

    if (!siteInfos.metaData.moderatorNotes) {
        siteInfos.metaData.moderatorNotes = {};
    }

    if (!siteInfos.metaData.customerNotes) {
        siteInfos.metaData.customerNotes = {};
    }

    console.log("getting prompt config from db...");
    // 1. get promptConfig for origin website
    let promptConfig = await getPromptConfigDB({ origin });

    if (!promptConfig) {
        console.error(
            "Promptconfig not found. Configuration is probably wrong"
        );
        return null;
    }

    // 2. figure out promptType for message based on input
    let promptType = getPromptTypeChatCompletion(siteInfos);

    console.log("minLength", siteInfos.metaData.minLength);

    // TODO: - create a new file for this logic in torchwood
    if (
        (promptType == PromptType.REACTIVATE_NEW_USER ||
            PromptType.REACTIVATE_USER ||
            (messages.length >= 1 &&
                messages.slice(-1)[0].type == "received" &&
                messages
                    .slice(-1)[0]
                    .text.includes("Lass uns das Eis brechen!"))) &&
        (origin == "torchwood" || origin == "b3" || origin == "df")
    ) {
        const template = await createTemplateTW(
            messages,
            siteInfos,
            origin,
            siteInfos.metaData.minLength ?? 40
        );

        if (template) {
            return template;
        } else {
            console.log("No template could be generated for torchwood");
        }
    }

    // 3. get promptid for type
    const promptId = promptConfig.config[promptType] as number;
    const summaryPromptId = promptConfig.config["summary"] as number;

    // Run both prompt fetches in parallel
    console.log(
        `getting prompt ${promptId}, promptType ${promptType} and summary prompt ${summaryPromptId} from db...`
    );
    const [prompt, summaryPrompt] = await Promise.all([
        getPromptDB(promptId as number),
        getPromptDB(summaryPromptId as number),
    ]);

    if (!prompt || !summaryPrompt) {
        console.error("Prompt not found. Configuration is probably wrong");
        return null;
    }

    // GPT creates structured notes from raw text - run both in parallel
    const [customerNotesFromRawText, moderatorNotesFromRawText] =
        await Promise.all([
            siteInfos.metaData.customerNotes?.rawText
                ? (console.log("structuring customer notes from raw text..."),
                  structureNotesFromRawtext(
                      siteInfos.metaData.customerNotes.rawText
                  ))
                : Promise.resolve(undefined),
            siteInfos.metaData.moderatorNotes?.rawText
                ? (console.log("structuring moderator notes from raw text..."),
                  structureNotesFromRawtext(
                      siteInfos.metaData.moderatorNotes.rawText
                  ))
                : Promise.resolve(undefined),
        ]);

    // MARK: - Computed metadata
    let customerCity: string | undefined;
    let customerCityLocation: Location | undefined;
    let computedMetadata: ComputedMetadata = params.computedMetadata ?? {};

    // use city from notes, if not use city from info
    if (
        siteInfos.metaData.moderatorNotes &&
        siteInfos.metaData.moderatorNotes.city
    ) {
        computedMetadata.nearbyCity = {
            name: siteInfos.metaData.moderatorNotes.city,
            lat: 0,
            long: 0,
        };
    } else if (moderatorNotesFromRawText?.city) {
        computedMetadata.nearbyCity = {
            name: moderatorNotesFromRawText.city,
            lat: 0,
            long: 0,
        };
    } else if (siteInfos.metaData.moderatorInfo.city) {
        computedMetadata.nearbyCity = {
            name: siteInfos.metaData.moderatorInfo.city,
            lat: 0,
            long: 0,
        };
    }

    const customerMessages = messages.filter((m) => m.type == "received");
    if (siteInfos.metaData.customerNotes?.city && origin != "love-room") {
        customerCity = siteInfos.metaData.customerNotes.city;
    } else if (customerNotesFromRawText?.city && origin != "love-room") {
        customerCity = customerNotesFromRawText.city;
    } else if (customerMessages.length > 0 && origin != "torchwood") {
        console.log("extracting user city from last 4 messages...");
        const city = await extractUserCity(
            messages
                .filter((m) => m.type != "system")
                .slice(-4) // Only take last 4 messages
                .map((m) => {
                    const role = m.type == "received" ? "user" : "assistant";
                    return `${role}: ${m.text}`;
                })
                .join("\n")
        );
        customerCity = city;
        siteInfos.metaData.customerNotes.city = city;
    }

    if (customerCity == undefined && siteInfos.metaData.customerInfo.city) {
        customerCity = siteInfos.metaData.customerInfo.city;
    }

    if (customerCity) {
        console.log("getting customer city location coordinates...");
        try {
            customerCityLocation = await getCityCoordinates(
                customerCity,
                siteInfos.metaData.customerInfo.country
            );
            computedMetadata.customerCity = {
                name: customerCity,
                lat: customerCityLocation?.lat ?? 0,
                long: customerCityLocation?.lng ?? 0,
            };
        } catch (error) {
            console.log(
                "error getting customer city location",
                error,
                siteInfos.metaData.customerInfo.city
            );
        }
    }

    let dailyInfo: DailyInfo | undefined = undefined;

    // TODO: Dailyinfo holidays not computed when location of city not found
    if (customerCityLocation && !computedMetadata.dailyInfo) {
        console.log("getting daily info...");
        dailyInfo = await getDailyInfo(
            customerCityLocation.lng,
            customerCityLocation.lat,
            siteInfos.metaData.customerInfo.country
        );
        computedMetadata.dailyInfo = dailyInfo;
    }

    if (customerCityLocation && !computedMetadata.nearbyCity) {
        console.log("computing nearby city...");
        computedMetadata.nearbyCity = await computeNearbyCity(
            computedMetadata.nearbyCity,
            customerCityLocation,
            prompt,
            computedMetadata
        );
    }

    // Calculate distance between cities if coordinates are available
    let distanceKm;
    if (
        customerCityLocation &&
        computedMetadata.nearbyCity?.lat &&
        computedMetadata.nearbyCity?.long
    ) {
        const R = 6371; // Earth's radius in km
        const lat1 = (customerCityLocation.lat * Math.PI) / 180;
        const lat2 = (computedMetadata.nearbyCity.lat * Math.PI) / 180;
        const dLat =
            ((computedMetadata.nearbyCity.lat - customerCityLocation.lat) *
                Math.PI) /
            180;
        const dLon =
            ((computedMetadata.nearbyCity.long - customerCityLocation.lng) *
                Math.PI) /
            180;

        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(lat1) *
                Math.cos(lat2) *
                Math.sin(dLon / 2) *
                Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        distanceKm = R * c;
    }

    console.log(
        `Customer City - From Notes: ${
            siteInfos.metaData.customerNotes?.city
        }, From Info: ${
            siteInfos.metaData.customerInfo.city
        }, Location: ${JSON.stringify(
            customerCityLocation
        )} | Nearby City - Name: ${
            computedMetadata.nearbyCity?.name
        }, Latitude: ${computedMetadata.nearbyCity?.lat}, Longitude: ${
            computedMetadata.nearbyCity?.long
        }, Distance (km): ${distanceKm?.toFixed(1)}`
    );

    let openaiMessages: OpenAI.ChatCompletionMessageParam[] &
        { imageSrc?: string }[] = [
        ...(await messagesToOpenAIMessages(origin, messages)),
    ];

    // MARK: - Current daytime
    let computedCurrentDayTime = currentDayTime();

    computedMetadata.currentDayTime = computedCurrentDayTime ?? undefined;

    // MARK: - Extract metadata
    const extractedMetadata = params.extractedMetadata
        ? params.extractedMetadata
        : await extractMetadata(
              siteInfos,
              computedMetadata,
              promptType,
              customerNotesFromRawText,
              moderatorNotesFromRawText
          );

    // MARK: - analyze profile pics
    let profilePicUrlMod = siteInfos.metaData.moderatorProfilePic
        ? siteInfos.metaData.moderatorProfilePic
        : undefined;
    let profilePicUrlUser = siteInfos.metaData.customerProfilePic
        ? siteInfos.metaData.customerProfilePic
        : undefined;

    console.log("analyzing profile pics...");
    const {
        profilePicUrlModAnalysis,
        profilePicUrlModKeywords,
        profilePicUrlUserAnalysis,
        profilePicUrlUserKeywords,
    } = await profilePicAnalysis(origin, profilePicUrlUser, profilePicUrlMod);

    extractedMetadata.moderatorProfilePicAnalysis = profilePicUrlModAnalysis;
    extractedMetadata.customerProfilePicAnalysis = profilePicUrlUserAnalysis;
    extractedMetadata.moderatorProfilePicKeywords = profilePicUrlModKeywords;
    extractedMetadata.customerProfilePicKeywords = profilePicUrlUserKeywords;

    console.log("getting nearby city from postal code...");
    if (siteInfos.metaData.customerInfo.postalCode && origin == "livecreator") {
        const nearbyCity = await getNearbyCityPostalCodePrefix(
            siteInfos.metaData.customerInfo.postalCode
        );
        if (nearbyCity) {
            extractedMetadata.moderatorCity = nearbyCity.name;
        }
    }

    // MARK: - MERGE PROMPTS WITH VARIABLES
    let mergedPrompt = mergePrompt(prompt, extractedMetadata);
    prompt.prompt = mergedPrompt;
    // ""

    let mergedSummaryPrompt = mergePrompt(summaryPrompt, extractedMetadata);
    summaryPrompt.prompt = mergedSummaryPrompt;

    if (promptType == PromptType.FRIEND_REQUEST) {
        openaiMessages[openaiMessages.length - 1] = {
            role: "assistant",
            content:
                "Information: Du hast eine Freundschaftsanfrage von deinem Gesprächspartner erhalten und angenommen. Du bist darüber glücklich und freust dich, deinen Gesprächspartner besser kennenzulernen.",
        };
        openaiMessages.push({
            role: "user",
            content: "Freundschaftsanfrage",
        });
    } else if (promptType == PromptType.FRIEND_REQUEST_ACCEPTED) {
        openaiMessages = [
            {
                role: "user",
                content: "Ich habe deine Freundschaftsanfrage angenommen",
            },
        ];
    }

    let config = prompt.ai_model_config;

    let resTextGemini: string | undefined = undefined;

    console.log("--- promptType", promptType);

    try {
        console.log("inferring gemini...");
        resTextGemini = await inferenceGemini(
            extractedMetadata,
            computedMetadata,
            siteInfos,
            origin,
            openaiMessages,
            promptType
        );
    } catch (error) {
        console.error("Error during testGemini:", error);
    }

    // MARK: All the control of the output happens here
    let resText: string | null = null;
    let resThinkingText: string | null = null;
    if (resTextGemini) {
        resText = resTextGemini;
    } else {
        let fallBackGeminiResText: string | null = null;
        if (
            promptType == PromptType.DEFAULT ||
            promptType == PromptType.KISS ||
            promptType == PromptType.MODERATOR_MALE
        ) {
            try {
                let fallBackGeminiRes = await fallbackGemini(
                    openaiMessages,
                    origin,
                    extractedMetadata,
                    siteInfos
                );

                if (fallBackGeminiRes && fallBackGeminiRes.success) {
                    fallBackGeminiResText =
                        fallBackGeminiRes.data?.generatedText ?? "";
                    resText = fallBackGeminiResText;
                }
            } catch (error) {
                console.error("Error during fallbackGemini:", error);
            }
        }

        if (!resText) {
            try {
                console.log(
                    "--- handleInferenceChatCompletion with gpt - resText from gemini:",
                    resTextGemini
                );

                // MARK: - only prompt for reactivations
                if (
                    promptType == PromptType.REACTIVATE_USER ||
                    promptType == PromptType.REACTIVATE_NEW_USER
                ) {
                    // reactivate user does not need any older messages
                    openaiMessages = [
                        {
                            role: "system",
                            content: prompt.prompt,
                        },
                    ];
                } else if (promptType == PromptType.ACTIVATION_MESSAGE) {
                    let userMessageForActivation = `Animiere den User! 

Hier sind Informationen über User:
    * {{customerName}}
    * {{customerAge}}
    * {{customerGender}}
    * {{customerCity}}
    * Profiltext (hat User über sich angegeben): {{customerProfileText}}
    * Notizen zum Gesprächspartner: {{customerInfos}}`;
                    userMessageForActivation = mergePrompt(
                        {
                            id: 102,
                            prompt: userMessageForActivation,
                            ai_model_config: {
                                model: "gpt-4o-mini",
                            },
                            ai_model: "gpt-4o-mini",
                        },
                        extractedMetadata
                    );
                    console.log(
                        "userMessageForActivation",
                        userMessageForActivation
                    );
                    openaiMessages = [
                        {
                            role: "system",
                            content: prompt.prompt,
                        },
                        {
                            role: "user",
                            content: userMessageForActivation,
                        },
                    ];
                } else {
                    openaiMessages = [
                        {
                            role: "system",
                            content: prompt.prompt,
                        },
                        // rest of the array are the messages
                        ...openaiMessages,
                    ];
                }
                const response = await handleInferenceChatCompletion(
                    openaiMessages,
                    config,
                    origin
                );

                if (!response) {
                    throw new Error("No response from inference");
                }

                const { content, thinkingContent } = response;
                resText = content;
                resThinkingText = thinkingContent;
                // Remove or declare resThinkingText if needed
            } catch (error) {
                console.error("Error during inference:", error);
                return await handleErrors({
                    resText: "",
                    alert: "API ERROR: Could not generate response " + error,
                    prompt: { name: siteInfos.metaData.type ?? "default", },
                    siteInfos,
                    extractedMetadata: {},
                    startedAt,
                    summary: {
                        assistant: {},
                        user: {}
                    },
                    disableAutoSend: true
                });
            }
        }
    }

    if (resText == "" || resText == null || resText.length < 40) {
        return await handleErrors({
            resText: "",
            siteInfos,
            alert: "API ERROR: Could not generate response. response empty",
            disableAutoSend: true,
            extractedMetadata: {},
            prompt: { name: siteInfos.metaData.type ?? "default", },
            startedAt,
            summary: {
                assistant: {},
                user: {}
            }
        });
    }

    console.log("rewrite init: ", {
        resText,
        origin,
        params: siteInfos.metaData.rewriteConfig,
        roomId: siteInfos.metaData.roomId,
    });

    let canRewrite = true;

    if (
        openaiMessages.length > 1 &&
        openaiMessages.filter((m) => m.role == "user").length <= 1
    ) {
        canRewrite = false;
    }

    // generate a new response with the rewrite attributes
    if (
        siteInfos.metaData.rewriteConfig &&
        (origin === "avz" || origin === "torchwood")
    ) {
        // config comes from client
        // THIS IS VERY IMPORTANT - WE NEED THAT
        try {
            const rewriteRes = await rewriteResponse(
                resText,
                siteInfos,
                origin
            );

            resText = rewriteRes;
        } catch (error) {
            console.error("Error during rewrite:", error);
        }
    } else if (canRewrite) {
        // we will test this only for myloves for now
        // rewrite for room id
        // if no roomid, just random adjectives
        console.log("rewriteConfig", siteInfos.metaData.rewriteConfig);

        const moderatorRewriteInfos: ModeratorRewriteInfos = {
            age: siteInfos.metaData.moderatorInfo.birthDate.age ?? 20,
            gender: siteInfos.metaData.moderatorInfo.gender,
        };

        const rewriteAttributes = await getRewrittenAttributes(
            origin,
            moderatorRewriteInfos,
            siteInfos.metaData.roomId
        );

        // if (origin == "myloves") {
        //     rewriteAttributes.push("Kürzere Antwort");
        // }

        siteInfos.metaData.rewriteConfig = {
            attributes: rewriteAttributes,
            rewriteAge: moderatorRewriteInfos.age.toString(),
            rewriteGender: moderatorRewriteInfos.gender,
            moderatorUsername: siteInfos.metaData.moderatorInfo.username,
        };

        console.log("rewriteAttributes", rewriteAttributes);

        const rewriteRes = await rewriteResponse(resText, siteInfos, origin);
        console.log(
            "rewriteRes",
            JSON.stringify({ resText, rewriteRes }, null, 2)
        );
        resText = rewriteRes;
    }

    const deltaTextLength = lengthChecker(
        resText!,
        siteInfos.metaData.minLength ?? 40,
        origin
    );

    // run it here once
    resText = cleanupOutput(resText, siteInfos);

    if (deltaTextLength < 0) {
        const resTextExtended = await ligamentolysis(
            resText!,
            siteInfos.metaData.minLength ?? 40,
            origin
        );
        if (resTextExtended) {
            resText = resTextExtended;
        } else {
            return await generateChatCompletion(
                {
                    ...params,
                    runCount: runCount + 1,
                    computedMetadata,
                    extractedMetadata,
                    startedAt,
                },
                user
            );
        }
    }

    // run it again after ligamentolysis
    resText = cleanupOutput(resText, siteInfos);

    let lessPermissiveUsers = [
        // "<EMAIL>",
        "<EMAIL>",
        // "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ];

    // Add blocked phrases check
    const blockedPhrases = checkForBlockedPhrases(resText!, siteInfos);

    // runs for every user
    if (blockedPhrases != false) {
        return await generateChatCompletion(
            {
                ...params,
                runCount: runCount + 1,
                computedMetadata,
                extractedMetadata,
                startedAt,
            },
            user
        );
    }

    let disableAutoSend = user
        ? lessPermissiveUsers.includes(user.email.toLowerCase())
        : false;

    let alert: string = "";

    let qwqOutput:
        | {
              passed: boolean;
              blocked_rules: string[];
              corrected_message?: string;
          }
        | undefined = undefined;

    try {
        qwqOutput = await checkQwqOutput(
            origin,
            resText!,
            promptType,
            siteInfos,
            extractedMetadata
        );
    } catch (error) {
        console.error("Error checking Qwq output", error);
        return await handleErrors({
            resText: resText ?? "",
            alert: "SERVER ERROR: " + error,
            disableAutoSend: true,
            summary: {
                assistant: {},
                user: {}
            },
            siteInfos,
            extractedMetadata: {},
            prompt,
            startedAt
        });
    }

    const blockedRulesAlert = {
        MINOR_DETECTED: "MINOR_DETECTED",
        CHECK_PHOTO: "CHECK_PHOTO",
    };

    if (
        qwqOutput?.blocked_rules?.some(
            (rule) => !Object.values(blockedRulesAlert).includes(rule)
        ) &&
        disableAutoSend == false
    ) {
        console.log(
            "###Conditions not met in thinking model. Restarting...### -",
            qwqOutput.blocked_rules.join(", ")
        );
        // ""
        // ""
        return await generateChatCompletion(
            {
                ...params,
                runCount: runCount + 1,
                computedMetadata,
                extractedMetadata,
                startedAt,
            },
            user
        );
    }

    if (qwqOutput?.blocked_rules?.includes("MINOR_DETECTED")) {
        return await handleErrors({
            resText: "",
            prompt: { name: siteInfos.metaData.type ?? "default", },
            siteInfos,
            extractedMetadata: {},
            summary: {
                assistant: {},
                user: {}
            },
            alert: "System detected a minor. Please let a human check the conversation.",
            startedAt,
            disableAutoSend: true
        });
    }

    openaiMessages.push({
        role: "assistant",
        content: resText,
    });

    // MARK: - Summary
    let summary = { user: {}, assistant: {} };
    let summaryResText = undefined;

    // These prompts need no summary
    const noSummaryPromptTypes: PromptType[] = [
        PromptType.REACTIVATE_USER,
        PromptType.REACTIVATE_NEW_USER,
        PromptType.KISS,
        PromptType.NO_PREVIOUS_MESSAGES,
        PromptType.OUTPUT_CONTROL,
    ];

    if (!noSummaryPromptTypes.includes(promptType) && messages.length > 0) {
        try {
            const summaryMessages = {
                messages: [
                    // take the last 4 messages that are not the system message for the summary
                    { role: "system", content: summaryPrompt.prompt },
                    ...openaiMessages
                        .filter((m) => m.role != "system")
                        .slice(-4),
                ],
            };
            const summaryRes = await createSummary(
                { messages: summaryMessages.messages },
                summaryPrompt!,
                origin,
                siteInfos.metaData.customerNotes?.name,
                siteInfos.metaData.moderatorNotes?.name,
                extractedMetadata
            );
            summary = summaryRes.summary;
            summaryResText = summaryRes.resText;

            // Analytics item for the AI response
            const analyticsItem = generateAnalyticsItem(
                summaryMessages.messages as ChatCompletionMessageParam[],
                siteInfos,
                extractedMetadata,
                computedMetadata,
                summaryPrompt,
                summaryResText
            );
            await createAnalyticsItem(analyticsItem, html);
        } catch (error) {
            console.error("Error creating summary", error);
            await sendLogSnag("choked", origin);
            return await handleErrors({
                resText: resText ?? "",
                prompt: { name: siteInfos.metaData.type ?? "default", },
                siteInfos,
                extractedMetadata: {},
                summary: {
                    assistant: {},
                    user: {}
                },
                alert: "SUMMARY CHOKED",
                startedAt,
                disableAutoSend: true
            });
        }
    }

    console.log("qwqOutput", qwqOutput);

    if (
        qwqOutput?.blocked_rules?.includes("CHECK_PHOTO") ||
        selectPhotoToSend
    ) {
        alert = "CUSTOMER WANTS PHOTO";
    }

    if (qwqOutput) {
        alert += `${
            qwqOutput.blocked_rules.length > 0
                ? qwqOutput.blocked_rules.join(", ")
                : ""
        }`;

        alert.replace("CHECK_PHOTO", "");
    }

    let assetsToSend: ImageAnalysisData[] | undefined = undefined;

    if (
        (origin == "livecreator" ||
            origin == "gold" ||
            origin == "whatsmeet" ||
            origin == "xloves" ||
            origin == "onlydates69" ||
            origin == "myloves" ||
            origin == "teddy" ||
            origin == "lacarna") &&
        alert.includes("CUSTOMER WANTS PHOTO") &&
        (!siteInfos.metaData.ins || siteInfos.metaData.ins >= 0)
    ) {
        try {
            let sendFromPrivateGallery = false;
            if (origin == "livecreator") {
                sendFromPrivateGallery = true;
            }

            let copiedMessages = openaiMessages.filter(
                (m, i) => i !== 0 && i !== openaiMessages.length - 1
            );

            copiedMessages = copiedMessages.filter((m) => m.role != "system");

            console.log(
                "finding assets",
                extractedMetadata.moderatorProfilePicKeywords
            );

            let searchQuery = `gender-${siteInfos.metaData.moderatorInfo.gender}`;

            // we only need gender, ethnicity, hair, bodytype
            if (
                extractedMetadata.moderatorProfilePicKeywords &&
                !sendFromPrivateGallery
            ) {
                let filteredKeywords =
                    extractedMetadata.moderatorProfilePicKeywords.filter(
                        (k) =>
                            k.toLowerCase().includes("gender") ||
                            k.toLowerCase().includes("ethnicity") ||
                            k.toLowerCase().includes("hair") ||
                            k.toLowerCase().includes("bodytype")
                    );
                searchQuery += `,${filteredKeywords.join(",")}`;
            }

            let findAssetPayload: {
                origin: string;
                searchQuery: string;
                messages: {
                    imageSrc: string;
                    role: string;
                    content: string;
                }[];
                moderatorId?: string;
                userId?: string;
                privateGallery?: string[];
            } = {
                origin,
                searchQuery,
                messages: copiedMessages
                    .map((m) => ({
                        //@ts-ignore
                        imageSrc: m.imageSrc,
                        role: m.role,
                        content: m.content?.toString() ?? "",
                    }))
                    .slice(-8),
                moderatorId: siteInfos.metaData.moderatorInfo.id,
                userId: siteInfos.metaData.customerInfo.id,
            };

            if (siteInfos.metaData.moderatorInfo.privateGallery) {
                findAssetPayload.privateGallery =
                    siteInfos.metaData.moderatorInfo.privateGallery;
            }

            assetsToSend = await findAsset(findAssetPayload);

            if (assetsToSend && assetsToSend.length > 0) {
                // Process up to 4 assets
                const assetsToProcess = assetsToSend.slice(0, 2);

                console.log("assetsToProcess", assetsToProcess);

                assetsToSend = await messageForAssets(
                    assetsToProcess,
                    siteInfos,
                    extractedMetadata,
                    computedMetadata,
                    copiedMessages,
                    origin
                );

                if (assetsToSend && assetsToSend.length > 0) {
                    assetsToSend = assetsToSend.filter(
                        (a) => a != undefined && a.resText
                    );
                }

                console.log("assetsToSend", assetsToSend);
            } else {
                // no asset available
                openaiMessages.push({
                    role: "assistant",
                    content: resText,
                });
            }
        } catch (error) {
            assetsToSend = undefined;
        }
    }

    alert = alert
        .split(",")
        .filter((a) => a != "CUSTOMER WANTS PHOTO" && a != "CHECK_PHOTO")
        .join(", ");
    alert = alert.replace("CHECK_PHOTO", "");
    alert = alert.replace("CUSTOMER WANTS PHOTO", "");

    // Remove all emojis from resText
    resText =
        resText?.replace(
            /[\u{1F300}-\u{1F9FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F000}-\u{1F02F}]|[\u{1F0A0}-\u{1F0FF}]|[\u{1F100}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{1F200}-\u{1F2FF}]|[\u{1F600}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F700}-\u{1F77F}]|[\u{1F780}-\u{1F7FF}]|[\u{1F800}-\u{1F8FF}]|[\u{1F900}-\u{1F9FF}]|[\u{1FA00}-\u{1FA6F}]|[\u{1FA70}-\u{1FAFF}]|[\u{1FAB0}-\u{1FABF}]|[\u{1FAC0}-\u{1FAFF}]|[\u{1FAD0}-\u{1FAFF}]|[\u{1FAE0}-\u{1FAFF}]|[\u{1FAF0}-\u{1FAFF}]|[\u{1FB00}-\u{1FBFF}]|[\u{1FC00}-\u{1FCFF}]|[\u{1FD00}-\u{1FDFF}]|[\u{1FE00}-\u{1FEFF}]|[\u{1FF00}-\u{1FFFF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{2B00}-\u{2BFF}]|[\u{2C00}-\u{2CFF}]|[\u{2D00}-\u{2DFF}]|[\u{2E00}-\u{2EFF}]|[\u{2F00}-\u{2FFF}]|[\u{3000}-\u{303F}]|[\u{3040}-\u{309F}]|[\u{30A0}-\u{30FF}]|[\u{3100}-\u{312F}]|[\u{3130}-\u{318F}]|[\u{3190}-\u{319F}]|[\u{31A0}-\u{31BF}]|[\u{31C0}-\u{31EF}]|[\u{31F0}-\u{31FF}]|[\u{3200}-\u{32FF}]|[\u{3300}-\u{33FF}]|[\u{3400}-\u{4DBF}]|[\u{4DC0}-\u{4DFF}]|[\u{4E00}-\u{9FFF}]|[\u{A000}-\u{A48F}]|[\u{A490}-\u{A4CF}]|[\u{A4D0}-\u{A4FF}]|[\u{A500}-\u{A63F}]|[\u{A640}-\u{A69F}]|[\u{A6A0}-\u{A6FF}]|[\u{A700}-\u{A71F}]|[\u{A720}-\u{A7FF}]|[\u{A800}-\u{A82F}]|[\u{A830}-\u{A83F}]|[\u{A840}-\u{A87F}]|[\u{A880}-\u{A8DF}]|[\u{A8E0}-\u{A8FF}]|[\u{A900}-\u{A92F}]|[\u{A930}-\u{A95F}]|[\u{A960}-\u{A97F}]|[\u{A980}-\u{A9DF}]|[\u{A9E0}-\u{A9FF}]|[\u{AA00}-\u{AA5F}]|[\u{AA60}-\u{AA7F}]|[\u{AA80}-\u{AADF}]|[\u{AAE0}-\u{AAFF}]|[\u{AB00}-\u{AB2F}]|[\u{AB30}-\u{AB6F}]|[\u{AB70}-\u{ABBF}]|[\u{ABC0}-\u{ABFF}]|[\u{AC00}-\u{D7AF}]|[\u{D7B0}-\u{D7FF}]|[\u{D800}-\u{DB7F}]|[\u{DB80}-\u{DBFF}]|[\u{DC00}-\u{DFFF}]|[\u{E000}-\u{F8FF}]|[\u{F900}-\u{FAFF}]|[\u{FB00}-\u{FB4F}]|[\u{FB50}-\u{FDFF}]|[\u{FE00}-\u{FE0F}]|[\u{FE10}-\u{FE1F}]|[\u{FE20}-\u{FE2F}]|[\u{FE30}-\u{FE4F}]|[\u{FE50}-\u{FE6F}]|[\u{FE70}-\u{FEFF}]|[\u{FF00}-\u{FFEF}]|[\u{FFF0}-\u{FFFF}]/gu,
            ""
        ) ?? "";

    if (origin != "avz") {
        resText = await appendEmoji(resText!, siteInfos);
    }

    // Analytics item for the AI response#
    const analyticsItem = generateAnalyticsItem(
        openaiMessages.slice(0, -1),
        siteInfos,
        extractedMetadata,
        computedMetadata,
        prompt,
        resText!
    );

    const analyticsItemId = await createAnalyticsItem(analyticsItem, html);

    return {
        resText: resText?.trim(),
        promptType: prompt.name ?? "Fehler: Kein Prompt",
        alert,
        summary,
        analyticsItemId: analyticsItemId ?? undefined,
        extractedMetadata,
        chatId: siteInfos.metaData.chatId,
        disableAutoSend,
        assetsToSend,
    };
};
