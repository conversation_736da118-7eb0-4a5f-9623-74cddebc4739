import {
  sendChatMaestroRequest,
  pollChatMaestroTask,
} from "./chatMaestroService";
import { SiteInfos, ExtractedMetadata } from "../../models/SiteInfos";
import { translateTextWithAI } from "../../controller/translateWithAI";

export interface SendToMaestroParams {
  resText: string;
  prompt: any;
  alert: string;
  siteInfos: SiteInfos;
  startedAt: Date;
  summary: {
    assistant: Record<string, string>;
    user: Record<string, string>;
  };
  extractedMetadata: ExtractedMetadata | undefined;
  disableAutoSend: boolean;
}

interface SendToMaestroResult {
  resText: string;
  promptType: string;
  alert?: string;
  summary?: {
    assistant: Record<string, string>;
    user: Record<string, string>;
  };
  extractedMetadata?: ExtractedMetadata;
  chatId?: string;
  disableAutoSend: boolean;
}

/**
 * Checks if the elapsed time since startedAt exceeds the threshold (2.5 minutes)
 * @param startedAt - The start time of the generation process
 * @param thresholdMs - Threshold in milliseconds (default: 150000ms = 2.5 minutes)
 * @returns boolean indicating if threshold is exceeded
 */
export function hasTimeThresholdExceeded(
  startedAt: Date,
  thresholdMs: number = 150000 // 2.5 minutes
): boolean {
  const elapsedTime = Date.now() - startedAt.getTime();
  const exceeded = elapsedTime > thresholdMs;

  console.log("[ChatMaestro] Time threshold check", {
    startedAt: startedAt.toISOString(),
    elapsedTime: `${elapsedTime / 1000}s`,
    threshold: `${thresholdMs / 1000}s`,
    exceeded
  });

  return exceeded;
}

/**
 * Sends the chat completion request to Chat Maestro and polls for the result
 * Only allowed for torchwood origin currently
 * @param params - Parameters needed for the maestro request
 * @returns Promise<SendToMaestroResult> - The formatted result matching the expected return structure
 */
export async function handleErrors(
  params: SendToMaestroParams
): Promise<SendToMaestroResult> {
  const {
    resText,
    prompt,
    alert,
    siteInfos,
    startedAt,
    summary,
    extractedMetadata,
    disableAutoSend,
  } = params;

  console.log("[ChatMaestro] handleErrors called", {
    origin: siteInfos.origin,
    sendToMaestro: siteInfos.metaData.sendToMaestro,
    alert,
    promptName: prompt.name
  });

  if (!siteInfos.metaData.sendToMaestro) {
    console.log("[ChatMaestro] sendToMaestro disabled, returning early", {
      origin: siteInfos.origin
    });
    return {
      resText: "",
      promptType: prompt.name ?? "Fehler: Kein Prompt",
      alert: alert,
      disableAutoSend: disableAutoSend,
    };
  } else {
    // Prepare the data payload for Chat Maestro
    const maestroData = {
      resText,
      siteInfos,
      alert,
      origin: siteInfos.origin,
      startedAt: startedAt.toISOString(),
    };

    console.log("[ChatMaestro] Sending to maestro", {
      origin: siteInfos.origin
    });

    const taskId = await sendChatMaestroRequest(
      maestroData,
      { useSupabaseAuth: true },
      false, // isPriority
      startedAt // Pass the flow start time for proper expiry calculation
    );

    console.log("[ChatMaestro] Task created successfully", {
      taskId,
      origin: siteInfos.origin
    });

    try {
      console.log("[ChatMaestro] Starting polling", { taskId });

      const result = await pollChatMaestroTask(taskId, {
        intervalMs: 3000, // Poll every 3 seconds
        maxDurationMs: 300000, // 5 minutes max polling
        authOptions: { useSupabaseAuth: true },
      });

      console.log("[ChatMaestro] Polling completed", {
        taskId,
        hasResult: !!result.resText,
        alert: result.alert
      });

      if (result.resText) {
        const language = result.language;
        let resTextRes = result.resText;

        if (resTextRes && language !== "DE") {
          console.log("[ChatMaestro] Translating result", { taskId, language });

          const translatedResText = await translateTextWithAI(resText);

          if (translatedResText) {
            resTextRes = translatedResText.data?.generatedText ?? "";
            console.log("[ChatMaestro] Translation completed", { taskId });
          } else {
            console.warn("[ChatMaestro] Translation failed", { taskId });
          }
        }

        const finalResult = {
          resText: resTextRes?.trim(),
          promptType: prompt.name ?? "Fehler: Kein Prompt",
          alert: "",
          summary,
          extractedMetadata: extractedMetadata,
          chatId: siteInfos.metaData.chatId,
          disableAutoSend: disableAutoSend,
        };

        console.log("[ChatMaestro] Success", { taskId });
        return finalResult;
      } else {
        console.warn("[ChatMaestro] No result text", { taskId });
        return {
          resText: "",
          promptType: prompt.name ?? "Fehler: Kein Prompt",
          alert: alert ?? "MAESTRO_NO_RESPONSE",
          summary,
          extractedMetadata: extractedMetadata,
          chatId: siteInfos.metaData.chatId,
          disableAutoSend: disableAutoSend,
        };
      }
    } catch (e) {
      console.error("[ChatMaestro] Error", {
        taskId,
        error: e instanceof Error ? e.message : String(e)
      });

      return {
        resText: "",
        promptType: prompt.name ?? "Fehler: Kein Prompt",
        alert: alert,
        summary,
        extractedMetadata: extractedMetadata,
        chatId: siteInfos.metaData.chatId,
        disableAutoSend: true,
      };
    }
  }
}
